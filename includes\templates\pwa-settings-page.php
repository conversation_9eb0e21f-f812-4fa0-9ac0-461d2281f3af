<?php
if (!defined('ABSPATH')) {
    exit;
}

// Check user capabilities
if (!current_user_can('manage_options')) {
    return;
}

// Get PWA status
$pwa_status = Q_PWA_Settings::get_pwa_status();
$manifest_validation = Q_PWA_Manifest::validate_manifest();
?>

<div class="wrap q-pwa-admin">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>



    <!-- Tabbed Settings Interface -->
    <div class="q-pwa-tabs-container">
        <nav class="q-pwa-tabs-nav">
            <button class="q-pwa-tab-button active" data-tab="general">
                <span class="dashicons dashicons-admin-generic"></span>
                General
            </button>
            <button class="q-pwa-tab-button" data-tab="appearance">
                <span class="dashicons dashicons-admin-appearance"></span>
                Appearance
            </button>
            <button class="q-pwa-tab-button" data-tab="offline">
                <span class="dashicons dashicons-cloud"></span>
                Offline
            </button>
            <button class="q-pwa-tab-button" data-tab="ios">
                <span class="dashicons dashicons-smartphone"></span>
                iOS
            </button>
            <button class="q-pwa-tab-button" data-tab="android">
                <span class="dashicons dashicons-tablet"></span>
                Android
            </button>
            <button class="q-pwa-tab-button" data-tab="desktop">
                <span class="dashicons dashicons-desktop"></span>
                Desktop
            </button>
            <button class="q-pwa-tab-button" data-tab="advanced">
                <span class="dashicons dashicons-admin-tools"></span>
                Advanced
            </button>
            <button class="q-pwa-tab-button" data-tab="status">
                <span class="dashicons dashicons-admin-generic"></span>
                PWA Status
            </button>
        </nav>

        <!-- Settings Form -->
        <form action="options.php" method="post" id="q-pwa-settings-form">
            <?php settings_fields('q_pwa_settings'); ?>

            <!-- General Tab -->
            <div class="q-pwa-tab-content active" id="tab-general">
                <div class="q-pwa-tab-header">
                    <h2>General Settings</h2>
                    <p>Configure basic Progressive Web App settings for your site.</p>
                </div>

                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">Enable PWA</th>
                            <td>
                                <?php Q_PWA_Settings::render_checkbox_field(['field' => 'q_pwa_enabled', 'description' => 'Enable Progressive Web App functionality']); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <div class="notice notice-info inline">
                    <p><strong>📋 Next Steps</strong></p>
                    <p>After enabling PWA functionality, configure your app settings in the following tabs:</p>
                    <ul>
                        <li><strong>Appearance:</strong> Set app name, description, and global settings</li>
                        <li><strong>iOS:</strong> Configure iOS-specific features and icons</li>
                        <li><strong>Android:</strong> Set up Android icons and theme colors</li>
                        <li><strong>Desktop:</strong> Configure desktop display and window settings</li>
                        <li><strong>Offline:</strong> Enable offline support and caching</li>
                    </ul>
                </div>

                <div class="q-pwa-quick-setup">
                    <h3>Quick Setup Guide</h3>
                    <div class="q-setup-steps">
                        <div class="setup-step">
                            <span class="step-number">1</span>
                            <div class="step-content">
                                <h4>Enable PWA</h4>
                                <p>Check the "Enable PWA" option above to activate Progressive Web App functionality.
                                </p>
                            </div>
                        </div>
                        <div class="setup-step">
                            <span class="step-number">2</span>
                            <div class="step-content">
                                <h4>Configure App Details</h4>
                                <p>Go to the <strong>Appearance</strong> tab to set your app name, description, and
                                    global settings.</p>
                            </div>
                        </div>
                        <div class="setup-step">
                            <span class="step-number">3</span>
                            <div class="step-content">
                                <h4>Platform-Specific Setup</h4>
                                <p>Configure platform-specific settings in the <strong>iOS</strong>,
                                    <strong>Android</strong>, and <strong>Desktop</strong> tabs.</p>
                            </div>
                        </div>
                        <div class="setup-step">
                            <span class="step-number">4</span>
                            <div class="step-content">
                                <h4>Test Your PWA</h4>
                                <p>Use the <strong>Advanced</strong> tab to test your PWA configuration and the
                                    <strong>Status</strong> tab to monitor requirements.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Appearance Tab -->
            <div class="q-pwa-tab-content" id="tab-appearance">
                <div class="q-pwa-tab-header">
                    <h2>Global Appearance Settings</h2>
                    <p>Configure global appearance settings that apply across all platforms. Platform-specific settings
                        are available in their respective tabs.</p>
                </div>

                <div class="notice notice-info inline">
                    <p><strong>ℹ️ Platform-Specific Settings</strong></p>
                    <p>For platform-specific appearance customization, please visit:</p>
                    <ul>
                        <li><strong>iOS Tab:</strong> iOS status bar style, splash background, touch icons</li>
                        <li><strong>Android Tab:</strong> Android theme color, background color, app icons</li>
                        <li><strong>Desktop Tab:</strong> Desktop display mode, orientation, window settings</li>
                    </ul>
                </div>

                <h3>Global App Information</h3>
                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">App Name</th>
                            <td>
                                <?php Q_PWA_Settings::render_text_field(['field' => 'q_pwa_app_name', 'description' => 'Full name of your app (max 45 characters) - used across all platforms']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Short Name</th>
                            <td>
                                <?php Q_PWA_Settings::render_text_field(['field' => 'q_pwa_app_short_name', 'description' => 'Short name for home screen (max 12 characters) - used across all platforms']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Description</th>
                            <td>
                                <?php Q_PWA_Settings::render_textarea_field(['field' => 'q_pwa_app_description', 'description' => 'Brief description of your app - used across all platforms']); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Offline Tab -->
            <div class="q-pwa-tab-content" id="tab-offline">
                <div class="q-pwa-tab-header">
                    <h2>Offline & Caching Settings</h2>
                    <p>Configure offline functionality and caching strategies.</p>
                </div>

                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">Enable Offline Support</th>
                            <td>
                                <?php Q_PWA_Settings::render_checkbox_field(['field' => 'q_pwa_offline_enabled', 'description' => 'Cache content for offline access']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Cache Strategy</th>
                            <td>
                                <?php Q_PWA_Settings::render_select_field([
                                    'field' => 'q_pwa_cache_strategy',
                                    'options' => [
                                        'cache_first' => 'Cache First (Faster)',
                                        'network_first' => 'Network First (Fresh Content)',
                                        'stale_while_revalidate' => 'Stale While Revalidate (Balanced)'
                                    ],
                                    'description' => 'How to handle caching for offline support'
                                ]); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <h3>Offline Page Customization</h3>
                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">Offline Page Title</th>
                            <td>
                                <?php Q_PWA_Settings::render_text_field(['field' => 'q_pwa_offline_title', 'description' => 'Title shown on the offline page']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Offline Page Message</th>
                            <td>
                                <?php Q_PWA_Settings::render_textarea_field(['field' => 'q_pwa_offline_message', 'description' => 'Message shown to users when they are offline']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Offline Page Icon</th>
                            <td>
                                <?php Q_PWA_Settings::render_text_field(['field' => 'q_pwa_offline_icon', 'description' => 'Emoji or icon to display on offline page (e.g., 📡, 🌐, ⚡)']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Show Cached Pages List</th>
                            <td>
                                <?php Q_PWA_Settings::render_checkbox_field(['field' => 'q_pwa_offline_show_cached_pages', 'description' => 'Display a list of available cached pages when offline']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Show Offline Tips</th>
                            <td>
                                <?php Q_PWA_Settings::render_checkbox_field(['field' => 'q_pwa_offline_show_tips', 'description' => 'Display helpful tips for users when they are offline']); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <h3>Offline Notifications</h3>
                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">Enable Offline Notifications</th>
                            <td>
                                <?php Q_PWA_Settings::render_checkbox_field(['field' => 'q_pwa_offline_notification_enabled', 'description' => 'Show notifications when users go offline or come back online']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Offline Notification Title</th>
                            <td>
                                <?php Q_PWA_Settings::render_text_field(['field' => 'q_pwa_offline_notification_title', 'description' => 'Title shown in the offline notification']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Offline Notification Message</th>
                            <td>
                                <?php Q_PWA_Settings::render_textarea_field(['field' => 'q_pwa_offline_notification_message', 'description' => 'Message shown to users when they go offline']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Offline Notification Icon</th>
                            <td>
                                <?php Q_PWA_Settings::render_text_field(['field' => 'q_pwa_offline_notification_icon', 'description' => 'Emoji or icon to display in offline notification (e.g., 📡, 🌐, ⚡)']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Enable Connection Monitoring</th>
                            <td>
                                <?php Q_PWA_Settings::render_checkbox_field(['field' => 'q_pwa_connection_monitoring_enabled', 'description' => 'Monitor connection quality and show warnings for slow connections']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Slow Connection Notifications</th>
                            <td>
                                <?php Q_PWA_Settings::render_checkbox_field(['field' => 'q_pwa_slow_connection_notification', 'description' => 'Show notifications when connection is slow or poor quality']); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Android Tab -->
            <div class="q-pwa-tab-content" id="tab-android">
                <div class="q-pwa-tab-header">
                    <h2>Android PWA Settings</h2>
                    <p>Configure Progressive Web App settings specifically for Android devices.</p>
                </div>

                <h3>Android App Icons</h3>
                <p>Upload icons for Android devices. Icons should be square and in PNG format.</p>
                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">192x192 Icon</th>
                            <td>
                                <?php Q_PWA_Settings::render_media_field(['field' => 'q_pwa_icon_192', 'description' => 'App icon for Android devices (192x192 pixels)']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">512x512 Icon</th>
                            <td>
                                <?php Q_PWA_Settings::render_media_field(['field' => 'q_pwa_icon_512', 'description' => 'App icon for splash screen (512x512 pixels)']); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <h3>Android Features</h3>
                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">Android Theme Color</th>
                            <td>
                                <?php Q_PWA_Settings::render_color_field(['field' => 'q_pwa_theme_color', 'description' => 'Primary theme color for Android status bar and task switcher']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Android Background Color</th>
                            <td>
                                <?php Q_PWA_Settings::render_color_field(['field' => 'q_pwa_background_color', 'description' => 'Background color for Android splash screen']); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <div class="q-pwa-android-info">
                    <h3>Android PWA Information</h3>
                    <div class="notice notice-info inline">
                        <p><strong>🤖 Android PWA Support</strong></p>
                        <ul>
                            <li>Android has excellent PWA support with Chrome</li>
                            <li>Users get native install prompts automatically</li>
                            <li>Full service worker and push notification support</li>
                            <li>App shortcuts and badging API support</li>
                            <li>Seamless integration with Android's app ecosystem</li>
                        </ul>
                    </div>

                    <div class="q-android-installation-guide">
                        <h4>Android Installation Instructions</h4>
                        <ol>
                            <li>Open your website in Chrome on Android</li>
                            <li>Look for the "Add to Home Screen" prompt</li>
                            <li>Tap "Install" when the banner appears</li>
                            <li>Or use Chrome menu → "Add to Home Screen"</li>
                            <li>Your PWA will appear in the app drawer</li>
                        </ol>
                    </div>
                </div>
            </div>

            <!-- Desktop Tab -->
            <div class="q-pwa-tab-content" id="tab-desktop">
                <div class="q-pwa-tab-header">
                    <h2>Desktop PWA Settings</h2>
                    <p>Configure Progressive Web App settings for desktop browsers and operating systems.</p>
                </div>

                <h3>Desktop Display Settings</h3>
                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">Desktop Display Mode</th>
                            <td>
                                <?php Q_PWA_Settings::render_select_field([
                                    'field' => 'q_pwa_display_mode',
                                    'options' => [
                                        'standalone' => 'Standalone (Recommended)',
                                        'fullscreen' => 'Fullscreen',
                                        'minimal-ui' => 'Minimal UI',
                                        'browser' => 'Browser'
                                    ],
                                    'description' => 'How the app should be displayed when launched on desktop'
                                ]); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Desktop Orientation</th>
                            <td>
                                <?php Q_PWA_Settings::render_select_field([
                                    'field' => 'q_pwa_orientation',
                                    'options' => [
                                        'any' => 'Any',
                                        'portrait' => 'Portrait',
                                        'landscape' => 'Landscape'
                                    ],
                                    'description' => 'Preferred screen orientation for desktop'
                                ]); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <h3>Desktop Window Settings</h3>
                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">Desktop Start URL</th>
                            <td>
                                <?php Q_PWA_Settings::render_text_field(['field' => 'q_pwa_start_url', 'description' => 'URL to load when desktop app is launched (relative to site root)']); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <div class="q-pwa-desktop-info">
                    <h3>Desktop PWA Information</h3>
                    <div class="notice notice-info inline">
                        <p><strong>💻 Desktop PWA Support</strong></p>
                        <ul>
                            <li>Chrome, Edge, and other Chromium browsers support desktop PWAs</li>
                            <li>PWAs can be installed from the address bar or browser menu</li>
                            <li>Desktop PWAs run in their own window without browser UI</li>
                            <li>Support for window controls, file handling, and system integration</li>
                            <li>Can be pinned to taskbar and appear in start menu/dock</li>
                        </ul>
                    </div>

                    <div class="q-desktop-installation-guide">
                        <h4>Desktop Installation Instructions</h4>
                        <ol>
                            <li>Visit your website in Chrome, Edge, or similar browser</li>
                            <li>Look for the install icon in the address bar</li>
                            <li>Click "Install" when prompted</li>
                            <li>Or use browser menu → "Install [App Name]"</li>
                            <li>The app will open in its own window</li>
                            <li>Find it in your applications/start menu</li>
                        </ol>
                    </div>
                </div>
            </div>

            <!-- iOS Support Tab -->
            <div class="q-pwa-tab-content" id="tab-ios">
                <div class="q-pwa-tab-header">
                    <h2>iOS Support Settings</h2>
                    <p>Configure iOS-specific Progressive Web App settings for better integration with Apple devices.
                    </p>
                </div>

                <h3>iOS Appearance</h3>
                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">iOS Status Bar Style</th>
                            <td>
                                <?php Q_PWA_Settings::render_select_field([
                                    'field' => 'q_pwa_ios_status_bar_style',
                                    'options' => [
                                        'default' => 'Default (Dark text)',
                                        'black' => 'Black (Dark text)',
                                        'black-translucent' => 'Black Translucent (Light text)'
                                    ],
                                    'description' => 'Controls the appearance of the iOS status bar when app is in standalone mode'
                                ]); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">iOS Splash Background</th>
                            <td>
                                <?php Q_PWA_Settings::render_color_field(['field' => 'q_pwa_ios_splash_background', 'description' => 'Background color for iOS splash screen']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Disable iOS Format Detection</th>
                            <td>
                                <?php Q_PWA_Settings::render_checkbox_field(['field' => 'q_pwa_ios_format_detection', 'description' => 'Prevent iOS from automatically detecting and formatting phone numbers, emails, etc.']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Enable iOS Startup Images</th>
                            <td>
                                <?php Q_PWA_Settings::render_checkbox_field(['field' => 'q_pwa_ios_startup_image_enabled', 'description' => 'Generate startup images for different iOS device sizes']); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <h3>iOS Touch Icons</h3>
                <p>Upload specific icons for iOS devices. These icons will be used when users add your PWA to their home
                    screen.</p>
                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">iOS Icon 57x57</th>
                            <td>
                                <?php Q_PWA_Settings::render_media_field(['field' => 'q_pwa_ios_icon_57', 'description' => 'iOS home screen icon for iPhone (57x57 pixels)']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">iOS Icon 72x72</th>
                            <td>
                                <?php Q_PWA_Settings::render_media_field(['field' => 'q_pwa_ios_icon_72', 'description' => 'iOS home screen icon for iPad (72x72 pixels)']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">iOS Icon 76x76</th>
                            <td>
                                <?php Q_PWA_Settings::render_media_field(['field' => 'q_pwa_ios_icon_76', 'description' => 'iOS home screen icon for iPad (76x76 pixels)']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">iOS Icon 114x114</th>
                            <td>
                                <?php Q_PWA_Settings::render_media_field(['field' => 'q_pwa_ios_icon_114', 'description' => 'iOS home screen icon for iPhone Retina (114x114 pixels)']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">iOS Icon 120x120</th>
                            <td>
                                <?php Q_PWA_Settings::render_media_field(['field' => 'q_pwa_ios_icon_120', 'description' => 'iOS home screen icon for iPhone Retina (120x120 pixels)']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">iOS Icon 144x144</th>
                            <td>
                                <?php Q_PWA_Settings::render_media_field(['field' => 'q_pwa_ios_icon_144', 'description' => 'iOS home screen icon for iPad Retina (144x144 pixels)']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">iOS Icon 152x152</th>
                            <td>
                                <?php Q_PWA_Settings::render_media_field(['field' => 'q_pwa_ios_icon_152', 'description' => 'iOS home screen icon for iPad Retina (152x152 pixels)']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">iOS Icon 180x180</th>
                            <td>
                                <?php Q_PWA_Settings::render_media_field(['field' => 'q_pwa_ios_icon_180', 'description' => 'iOS home screen icon for iPhone 6 Plus (180x180 pixels)']); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <div class="q-pwa-ios-info">
                    <h3>iOS PWA Information</h3>
                    <div class="notice notice-info inline">
                        <p><strong>📱 iOS PWA Support</strong></p>
                        <ul>
                            <li>iOS has limited PWA support compared to Android</li>
                            <li>Users can add your PWA to home screen via Safari's share menu</li>
                            <li>iOS PWAs run in a standalone browser context</li>
                            <li>Push notifications are supported on iOS 16.4+ with limitations</li>
                            <li>Service workers have some restrictions on iOS</li>
                        </ul>
                    </div>

                    <div class="q-ios-installation-guide">
                        <h4>iOS Installation Instructions</h4>
                        <ol>
                            <li>Open your website in Safari on iOS</li>
                            <li>Tap the Share button (square with arrow)</li>
                            <li>Scroll down and tap "Add to Home Screen"</li>
                            <li>Customize the name if needed and tap "Add"</li>
                            <li>Your PWA will appear on the home screen</li>
                        </ol>
                    </div>
                </div>
            </div>

            <!-- Advanced Tab -->
            <div class="q-pwa-tab-content" id="tab-advanced">
                <div class="q-pwa-tab-header">
                    <h2>Advanced Settings & Testing</h2>
                    <p>Advanced PWA configuration and testing tools.</p>
                </div>

                <div class="q-pwa-testing-tools">
                    <h3>PWA Testing Tools</h3>
                    <p>Use these tools to test your PWA configuration:</p>

                    <div class="q-pwa-tools">
                        <a href="<?php echo esc_url(Q_PWA_Manifest::get_manifest_url()); ?>" target="_blank"
                            class="button">
                            <span class="dashicons dashicons-media-code"></span>
                            View Manifest
                        </a>
                        <a href="https://web.dev/measure/" target="_blank" class="button">
                            <span class="dashicons dashicons-performance"></span>
                            Test with Lighthouse
                        </a>
                        <a href="https://manifest-validator.appspot.com/" target="_blank" class="button">
                            <span class="dashicons dashicons-yes-alt"></span>
                            Validate Manifest
                        </a>
                        <button type="button" class="button" id="q-test-manifest">
                            <span class="dashicons dashicons-search"></span>
                            Test Manifest
                        </button>
                        <button type="button" class="button" id="q-preview-pwa">
                            <span class="dashicons dashicons-external"></span>
                            Preview PWA
                        </button>
                    </div>

                    <div id="q-manifest-preview" style="display: none; margin-top: 15px;">
                        <h4>Manifest Preview</h4>
                        <pre id="q-manifest-content"
                            style="background: #f1f1f1; padding: 15px; overflow-x: auto; border-radius: 4px;"></pre>
                    </div>
                </div>

                <div class="q-pwa-installation-guide">
                    <h3>Installation Instructions</h3>
                    <p>Once your PWA is configured, users can install it on their devices:</p>

                    <div class="q-install-instructions">
                        <div class="instruction-group">
                            <h4>📱 Mobile (Android/iOS)</h4>
                            <ol>
                                <li>Open your website in Chrome (Android) or Safari (iOS)</li>
                                <li>Tap the browser menu (⋮ or share button)</li>
                                <li>Select "Add to Home Screen" or "Install App"</li>
                                <li>Follow the prompts to install</li>
                            </ol>
                        </div>

                        <div class="instruction-group">
                            <h4>💻 Desktop (Chrome/Edge)</h4>
                            <ol>
                                <li>Visit your website in Chrome or Edge</li>
                                <li>Look for the install icon in the address bar</li>
                                <li>Click "Install" when prompted</li>
                                <li>The app will be added to your applications</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <!-- PWA Status Tab -->
            <div class="q-pwa-tab-content" id="tab-status">
                <div class="q-pwa-tab-header">
                    <h2>PWA Status</h2>
                    <p>Monitor your Progressive Web App configuration and requirements.</p>
                </div>

                <!-- PWA Status Dashboard -->
                <div class="q-pwa-status-card">
                    <div class="status-header">
                        <h2><span class="dashicons dashicons-smartphone"></span> PWA Status</h2>
                        <div class="status-badge <?php echo $pwa_status['ready'] ? 'ready' : 'incomplete'; ?>">
                            <?php echo $pwa_status['ready'] ? 'Ready' : 'Incomplete'; ?>
                        </div>
                    </div>
                    <div class="q-pwa-progress">
                        <div class="progress-bar">
                            <div class="progress-fill"
                                style="width: <?php echo esc_attr($pwa_status['percentage']); ?>%"></div>
                        </div>
                        <p class="progress-text"><?php echo esc_html($pwa_status['percentage']); ?>% Complete</p>
                    </div>

                    <div class="q-pwa-requirements">
                        <h3>Requirements Checklist</h3>
                        <ul>
                            <li class="<?php echo $pwa_status['requirements']['https'] ? 'completed' : 'pending'; ?>">
                                <span
                                    class="dashicons <?php echo $pwa_status['requirements']['https'] ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>"></span>
                                HTTPS Enabled
                                <?php if (!$pwa_status['requirements']['https']): ?>
                                    <small>Your site must use HTTPS for PWA functionality</small>
                                <?php endif; ?>
                            </li>
                            <li
                                class="<?php echo $pwa_status['requirements']['manifest'] ? 'completed' : 'pending'; ?>">
                                <span
                                    class="dashicons <?php echo $pwa_status['requirements']['manifest'] ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>"></span>
                                PWA Enabled
                                <?php if (!$pwa_status['requirements']['manifest']): ?>
                                    <small>Enable PWA functionality in the General tab</small>
                                <?php endif; ?>
                            </li>
                            <li
                                class="<?php echo $pwa_status['requirements']['service_worker'] ? 'completed' : 'pending'; ?>">
                                <span
                                    class="dashicons <?php echo $pwa_status['requirements']['service_worker'] ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>"></span>
                                Service Worker Installed
                                <?php if (!$pwa_status['requirements']['service_worker']): ?>
                                    <small>Service worker is required for offline functionality</small>
                                <?php endif; ?>
                            </li>
                            <li class="<?php echo $pwa_status['requirements']['icons'] ? 'completed' : 'pending'; ?>">
                                <span
                                    class="dashicons <?php echo $pwa_status['requirements']['icons'] ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>"></span>
                                App Icons Configured
                                <?php if (!$pwa_status['requirements']['icons']): ?>
                                    <small>Upload 192x192 and 512x512 icons in the Icons tab</small>
                                <?php endif; ?>
                            </li>
                        </ul>
                    </div>

                    <?php if ($pwa_status['ready']): ?>
                        <div class="notice notice-success inline">
                            <p><strong>🎉 Your PWA is ready!</strong> Users can now install your app on their devices.</p>
                        </div>
                    <?php else: ?>
                        <div class="notice notice-warning inline">
                            <p><strong>⚠️ PWA Setup Incomplete</strong> Complete the requirements above to enable full PWA
                                functionality.</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Manifest Validation -->
                <?php if (!$manifest_validation['valid']): ?>
                    <div class="notice notice-error">
                        <h3><span class="dashicons dashicons-warning"></span> Manifest Validation Errors</h3>
                        <ul>
                            <?php foreach ($manifest_validation['errors'] as $error): ?>
                                <li><?php echo esc_html($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Save Button -->
            <div class="q-pwa-actions">
                <?php submit_button('Save PWA Settings', 'primary', 'submit', false); ?>
            </div>
        </form>
    </div>
</div>

<script>
    // Fallback tab functionality in case external JS doesn't load
    jQuery(document).ready(function ($) {
        console.log('PWA Admin: Initializing tab functionality');

        // Simple tab switching
        $('.q-pwa-tab-button').on('click', function (e) {
            e.preventDefault();
            console.log('PWA Admin: Tab clicked');

            const tabId = $(this).data('tab');
            console.log('PWA Admin: Switching to tab:', tabId);

            // Remove active class from all tabs and buttons
            $('.q-pwa-tab-button').removeClass('active');
            $('.q-pwa-tab-content').removeClass('active');

            // Add active class to clicked button and corresponding tab
            $(this).addClass('active');
            $('#tab-' + tabId).addClass('active');

            console.log('PWA Admin: Tab switched successfully');
        });

        // Test if elements exist
        console.log('PWA Admin: Tab buttons found:', $('.q-pwa-tab-button').length);
        console.log('PWA Admin: Tab contents found:', $('.q-pwa-tab-content').length);
        console.log('PWA Admin: jQuery version:', $.fn.jquery);
        console.log('PWA Admin: qPWAAdmin object:', typeof qPWAAdmin !== 'undefined' ? qPWAAdmin : 'undefined');

        // List all tab buttons and their data-tab values
        $('.q-pwa-tab-button').each(function (index) {
            console.log('PWA Admin: Tab button', index, '- data-tab:', $(this).data('tab'), '- text:', $(this).text().trim());
        });

        // List all tab contents and their IDs
        $('.q-pwa-tab-content').each(function (index) {
            console.log('PWA Admin: Tab content', index, '- ID:', $(this).attr('id'), '- classes:', $(this).attr('class'));
        });
    });
</script>

<style>
    /* Fallback styles for tabs */
    .q-pwa-tabs-container {
        background: #fff;
        border: 1px solid #ccd0d4;
        border-radius: 4px;
        margin-top: 20px;
    }

    .q-pwa-tabs-nav {
        display: flex;
        background: #f6f7f7;
        border-bottom: 1px solid #ccd0d4;
        margin: 0;
        padding: 0;
    }

    .q-pwa-tab-button {
        background: none;
        border: none;
        padding: 12px 20px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 14px;
        color: #50575e;
        transition: all 0.2s ease;
        border-bottom: 3px solid transparent;
    }

    .q-pwa-tab-button:hover {
        background: #fff;
        color: #1d2327;
    }

    .q-pwa-tab-button.active {
        background: #fff;
        color: #2271b1;
        border-bottom-color: #2271b1;
    }

    .q-pwa-tab-content {
        display: none;
        padding: 20px;
    }

    .q-pwa-tab-content.active {
        display: block;
    }

    .q-pwa-tab-header {
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #f1f1f1;
    }

    .q-pwa-tab-header h2 {
        margin: 0 0 8px 0;
        color: #1d2327;
    }

    .q-pwa-tab-header p {
        margin: 0;
        color: #646970;
    }

    .q-pwa-actions {
        margin: 20px 0 0 0;
        padding: 15px 20px;
        background: #f6f7f7;
        border-top: 1px solid #ccd0d4;
    }
</style>