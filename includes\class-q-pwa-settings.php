<?php
if (!defined('ABSPATH')) {
    exit;
}

class Q_PWA_Settings
{
    public static function init()
    {
        add_action('admin_menu', [self::class, 'add_pwa_settings_page']);
        add_action('admin_init', [self::class, 'register_pwa_settings']);
        add_action('wp_enqueue_scripts', [self::class, 'enqueue_pwa_scripts']);
        add_action('wp_head', [self::class, 'add_pwa_meta_tags']);
        add_action('wp_head', [self::class, 'add_manifest_link']);
        add_action('admin_enqueue_scripts', [self::class, 'enqueue_admin_scripts']);

        // Regenerate offline page when settings are updated
        add_action('update_option_q_pwa_offline_title', [self::class, 'regenerate_offline_page']);
        add_action('update_option_q_pwa_offline_message', [self::class, 'regenerate_offline_page']);
        add_action('update_option_q_pwa_offline_icon', [self::class, 'regenerate_offline_page']);
        add_action('update_option_q_pwa_offline_show_cached_pages', [self::class, 'regenerate_offline_page']);
        add_action('update_option_q_pwa_offline_show_tips', [self::class, 'regenerate_offline_page']);
        add_action('update_option_q_pwa_theme_color', [self::class, 'regenerate_offline_page']);
        add_action('update_option_q_pwa_app_name', [self::class, 'regenerate_offline_page']);

        // Regenerate offline page when offline notification settings are updated
        add_action('update_option_q_pwa_offline_notification_enabled', [self::class, 'regenerate_offline_page']);
        add_action('update_option_q_pwa_offline_notification_title', [self::class, 'regenerate_offline_page']);
        add_action('update_option_q_pwa_offline_notification_message', [self::class, 'regenerate_offline_page']);
        add_action('update_option_q_pwa_offline_notification_icon', [self::class, 'regenerate_offline_page']);
        add_action('update_option_q_pwa_connection_monitoring_enabled', [self::class, 'regenerate_offline_page']);
        add_action('update_option_q_pwa_slow_connection_notification', [self::class, 'regenerate_offline_page']);
    }

    public static function add_pwa_settings_page()
    {
        add_submenu_page(
            'options-general.php',
            'PWA Settings',
            'PWA Settings',
            'manage_options',
            'q-pwa-settings',
            [self::class, 'render_pwa_settings_page']
        );
    }

    public static function register_pwa_settings()
    {
        // PWA Basic Settings
        register_setting('q_pwa_settings', 'q_pwa_enabled', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_app_name', [
            'type' => 'string',
            'default' => get_bloginfo('name'),
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_app_short_name', [
            'type' => 'string',
            'default' => get_bloginfo('name'),
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_app_description', [
            'type' => 'string',
            'default' => get_bloginfo('description'),
            'sanitize_callback' => 'sanitize_textarea_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_theme_color', [
            'type' => 'string',
            'default' => '#000000',
            'sanitize_callback' => 'sanitize_hex_color'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_background_color', [
            'type' => 'string',
            'default' => '#ffffff',
            'sanitize_callback' => 'sanitize_hex_color'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_display_mode', [
            'type' => 'string',
            'default' => 'standalone',
            'sanitize_callback' => [self::class, 'sanitize_display_mode']
        ]);

        register_setting('q_pwa_settings', 'q_pwa_orientation', [
            'type' => 'string',
            'default' => 'any',
            'sanitize_callback' => [self::class, 'sanitize_orientation']
        ]);

        register_setting('q_pwa_settings', 'q_pwa_start_url', [
            'type' => 'string',
            'default' => '/',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        // Offline Settings
        register_setting('q_pwa_settings', 'q_pwa_offline_enabled', [
            'type' => 'boolean',
            'default' => true,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_cache_strategy', [
            'type' => 'string',
            'default' => 'cache_first',
            'sanitize_callback' => [self::class, 'sanitize_cache_strategy']
        ]);

        register_setting('q_pwa_settings', 'q_pwa_offline_page', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        // Offline Page Customization Settings
        register_setting('q_pwa_settings', 'q_pwa_offline_title', [
            'type' => 'string',
            'default' => 'You\'re Offline',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_offline_message', [
            'type' => 'string',
            'default' => 'It looks like you\'ve lost your internet connection. Don\'t worry, you can still browse some cached content!',
            'sanitize_callback' => 'sanitize_textarea_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_offline_icon', [
            'type' => 'string',
            'default' => '📡',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        // Offline Notification Settings
        register_setting('q_pwa_settings', 'q_pwa_offline_notification_enabled', [
            'type' => 'boolean',
            'default' => true,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_offline_notification_title', [
            'type' => 'string',
            'default' => 'You\'re Offline',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_offline_notification_message', [
            'type' => 'string',
            'default' => 'Some features may be limited while offline.',
            'sanitize_callback' => 'sanitize_textarea_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_offline_notification_icon', [
            'type' => 'string',
            'default' => '📡',
            'sanitize_callback' => 'sanitize_text_field'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_connection_monitoring_enabled', [
            'type' => 'boolean',
            'default' => true,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_slow_connection_notification', [
            'type' => 'boolean',
            'default' => true,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_offline_show_cached_pages', [
            'type' => 'boolean',
            'default' => true,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_offline_show_tips', [
            'type' => 'boolean',
            'default' => true,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        // Icon Settings
        register_setting('q_pwa_settings', 'q_pwa_icon_192', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_icon_512', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        // iOS-specific Settings
        register_setting('q_pwa_settings', 'q_pwa_ios_status_bar_style', [
            'type' => 'string',
            'default' => 'default',
            'sanitize_callback' => [self::class, 'sanitize_ios_status_bar_style']
        ]);

        register_setting('q_pwa_settings', 'q_pwa_ios_splash_background', [
            'type' => 'string',
            'default' => '#ffffff',
            'sanitize_callback' => 'sanitize_hex_color'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_ios_icon_57', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_ios_icon_72', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_ios_icon_76', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_ios_icon_114', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_ios_icon_120', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_ios_icon_144', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_ios_icon_152', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_ios_icon_180', [
            'type' => 'string',
            'default' => '',
            'sanitize_callback' => 'esc_url_raw'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_ios_startup_image_enabled', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        register_setting('q_pwa_settings', 'q_pwa_ios_format_detection', [
            'type' => 'boolean',
            'default' => false,
            'sanitize_callback' => 'rest_sanitize_boolean'
        ]);

        // Android-specific Settings (using existing theme/background color settings)
        // These are already registered above as q_pwa_theme_color and q_pwa_background_color

        // Desktop-specific Settings (using existing display/orientation settings)
        // These are already registered above as q_pwa_display_mode and q_pwa_orientation

        // Add settings sections
        add_settings_section(
            'q_pwa_basic_section',
            'Basic PWA Settings',
            [self::class, 'render_basic_section_description'],
            'q_pwa_settings'
        );

        add_settings_section(
            'q_pwa_appearance_section',
            'Appearance Settings',
            [self::class, 'render_appearance_section_description'],
            'q_pwa_settings'
        );

        add_settings_section(
            'q_pwa_offline_section',
            'Offline & Caching Settings',
            [self::class, 'render_offline_section_description'],
            'q_pwa_settings'
        );

        add_settings_section(
            'q_pwa_icons_section',
            'App Icons',
            [self::class, 'render_icons_section_description'],
            'q_pwa_settings'
        );

        add_settings_section(
            'q_pwa_ios_section',
            'iOS Support',
            [self::class, 'render_ios_section_description'],
            'q_pwa_settings'
        );

        add_settings_section(
            'q_pwa_android_section',
            'Android Support',
            [self::class, 'render_android_section_description'],
            'q_pwa_settings'
        );

        add_settings_section(
            'q_pwa_desktop_section',
            'Desktop Support',
            [self::class, 'render_desktop_section_description'],
            'q_pwa_settings'
        );

        // Add settings fields
        self::add_settings_fields();
    }

    private static function add_settings_fields()
    {
        // Basic Settings Fields
        add_settings_field(
            'q_pwa_enabled',
            'Enable PWA',
            [self::class, 'render_checkbox_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            ['field' => 'q_pwa_enabled', 'description' => 'Enable Progressive Web App functionality']
        );

        add_settings_field(
            'q_pwa_app_name',
            'App Name',
            [self::class, 'render_text_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            ['field' => 'q_pwa_app_name', 'description' => 'Full name of your app (max 45 characters)']
        );

        add_settings_field(
            'q_pwa_app_short_name',
            'Short Name',
            [self::class, 'render_text_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            ['field' => 'q_pwa_app_short_name', 'description' => 'Short name for home screen (max 12 characters)']
        );

        add_settings_field(
            'q_pwa_app_description',
            'Description',
            [self::class, 'render_textarea_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            ['field' => 'q_pwa_app_description', 'description' => 'Brief description of your app']
        );

        add_settings_field(
            'q_pwa_start_url',
            'Start URL',
            [self::class, 'render_text_field'],
            'q_pwa_settings',
            'q_pwa_basic_section',
            ['field' => 'q_pwa_start_url', 'description' => 'URL to load when app is launched (relative to site root)']
        );

        // Appearance Settings Fields
        add_settings_field(
            'q_pwa_theme_color',
            'Theme Color',
            [self::class, 'render_color_field'],
            'q_pwa_settings',
            'q_pwa_appearance_section',
            ['field' => 'q_pwa_theme_color', 'description' => 'Primary theme color for the app']
        );

        add_settings_field(
            'q_pwa_background_color',
            'Background Color',
            [self::class, 'render_color_field'],
            'q_pwa_settings',
            'q_pwa_appearance_section',
            ['field' => 'q_pwa_background_color', 'description' => 'Background color for splash screen']
        );

        add_settings_field(
            'q_pwa_display_mode',
            'Display Mode',
            [self::class, 'render_select_field'],
            'q_pwa_settings',
            'q_pwa_appearance_section',
            [
                'field' => 'q_pwa_display_mode',
                'options' => [
                    'standalone' => 'Standalone (Recommended)',
                    'fullscreen' => 'Fullscreen',
                    'minimal-ui' => 'Minimal UI',
                    'browser' => 'Browser'
                ],
                'description' => 'How the app should be displayed when launched'
            ]
        );

        add_settings_field(
            'q_pwa_orientation',
            'Orientation',
            [self::class, 'render_select_field'],
            'q_pwa_settings',
            'q_pwa_appearance_section',
            [
                'field' => 'q_pwa_orientation',
                'options' => [
                    'any' => 'Any',
                    'portrait' => 'Portrait',
                    'landscape' => 'Landscape'
                ],
                'description' => 'Preferred screen orientation'
            ]
        );

        // Offline Settings Fields
        add_settings_field(
            'q_pwa_offline_enabled',
            'Enable Offline Support',
            [self::class, 'render_checkbox_field'],
            'q_pwa_settings',
            'q_pwa_offline_section',
            ['field' => 'q_pwa_offline_enabled', 'description' => 'Cache content for offline access']
        );

        add_settings_field(
            'q_pwa_cache_strategy',
            'Cache Strategy',
            [self::class, 'render_select_field'],
            'q_pwa_settings',
            'q_pwa_offline_section',
            [
                'field' => 'q_pwa_cache_strategy',
                'options' => [
                    'cache_first' => 'Cache First (Faster)',
                    'network_first' => 'Network First (Fresh Content)',
                    'stale_while_revalidate' => 'Stale While Revalidate (Balanced)'
                ],
                'description' => 'How to handle caching for offline support'
            ]
        );

        // Offline Page Customization Fields
        add_settings_field(
            'q_pwa_offline_title',
            'Offline Page Title',
            [self::class, 'render_text_field'],
            'q_pwa_settings',
            'q_pwa_offline_section',
            ['field' => 'q_pwa_offline_title', 'description' => 'Title shown on the offline page']
        );

        add_settings_field(
            'q_pwa_offline_message',
            'Offline Page Message',
            [self::class, 'render_textarea_field'],
            'q_pwa_settings',
            'q_pwa_offline_section',
            ['field' => 'q_pwa_offline_message', 'description' => 'Message shown to users when they are offline']
        );

        add_settings_field(
            'q_pwa_offline_icon',
            'Offline Page Icon',
            [self::class, 'render_text_field'],
            'q_pwa_settings',
            'q_pwa_offline_section',
            ['field' => 'q_pwa_offline_icon', 'description' => 'Emoji or icon to display on offline page (e.g., 📡, 🌐, ⚡)']
        );

        add_settings_field(
            'q_pwa_offline_show_cached_pages',
            'Show Cached Pages List',
            [self::class, 'render_checkbox_field'],
            'q_pwa_settings',
            'q_pwa_offline_section',
            ['field' => 'q_pwa_offline_show_cached_pages', 'description' => 'Display a list of available cached pages when offline']
        );

        add_settings_field(
            'q_pwa_offline_show_tips',
            'Show Offline Tips',
            [self::class, 'render_checkbox_field'],
            'q_pwa_settings',
            'q_pwa_offline_section',
            ['field' => 'q_pwa_offline_show_tips', 'description' => 'Display helpful tips for users when they are offline']
        );

        // Offline Notification Fields
        add_settings_field(
            'q_pwa_offline_notification_enabled',
            'Enable Offline Notifications',
            [self::class, 'render_checkbox_field'],
            'q_pwa_settings',
            'q_pwa_offline_section',
            ['field' => 'q_pwa_offline_notification_enabled', 'description' => 'Show notifications when users go offline or come back online']
        );

        add_settings_field(
            'q_pwa_offline_notification_title',
            'Offline Notification Title',
            [self::class, 'render_text_field'],
            'q_pwa_settings',
            'q_pwa_offline_section',
            ['field' => 'q_pwa_offline_notification_title', 'description' => 'Title shown in the offline notification']
        );

        add_settings_field(
            'q_pwa_offline_notification_message',
            'Offline Notification Message',
            [self::class, 'render_textarea_field'],
            'q_pwa_settings',
            'q_pwa_offline_section',
            ['field' => 'q_pwa_offline_notification_message', 'description' => 'Message shown to users when they go offline']
        );

        add_settings_field(
            'q_pwa_offline_notification_icon',
            'Offline Notification Icon',
            [self::class, 'render_text_field'],
            'q_pwa_settings',
            'q_pwa_offline_section',
            ['field' => 'q_pwa_offline_notification_icon', 'description' => 'Emoji or icon to display in offline notification (e.g., 📡, 🌐, ⚡)']
        );

        add_settings_field(
            'q_pwa_connection_monitoring_enabled',
            'Enable Connection Monitoring',
            [self::class, 'render_checkbox_field'],
            'q_pwa_settings',
            'q_pwa_offline_section',
            ['field' => 'q_pwa_connection_monitoring_enabled', 'description' => 'Monitor connection quality and show warnings for slow connections']
        );

        add_settings_field(
            'q_pwa_slow_connection_notification',
            'Slow Connection Notifications',
            [self::class, 'render_checkbox_field'],
            'q_pwa_settings',
            'q_pwa_offline_section',
            ['field' => 'q_pwa_slow_connection_notification', 'description' => 'Show notifications when connection is slow or poor quality']
        );

        // Icon Settings Fields
        add_settings_field(
            'q_pwa_icon_192',
            '192x192 Icon',
            [self::class, 'render_media_field'],
            'q_pwa_settings',
            'q_pwa_icons_section',
            ['field' => 'q_pwa_icon_192', 'description' => 'App icon for Android devices (192x192 pixels)']
        );

        add_settings_field(
            'q_pwa_icon_512',
            '512x512 Icon',
            [self::class, 'render_media_field'],
            'q_pwa_settings',
            'q_pwa_icons_section',
            ['field' => 'q_pwa_icon_512', 'description' => 'App icon for splash screen (512x512 pixels)']
        );

        // iOS Settings Fields
        add_settings_field(
            'q_pwa_ios_status_bar_style',
            'iOS Status Bar Style',
            [self::class, 'render_select_field'],
            'q_pwa_settings',
            'q_pwa_ios_section',
            [
                'field' => 'q_pwa_ios_status_bar_style',
                'options' => [
                    'default' => 'Default (Dark text)',
                    'black' => 'Black (Dark text)',
                    'black-translucent' => 'Black Translucent (Light text)'
                ],
                'description' => 'Controls the appearance of the iOS status bar when app is in standalone mode'
            ]
        );

        add_settings_field(
            'q_pwa_ios_splash_background',
            'iOS Splash Background',
            [self::class, 'render_color_field'],
            'q_pwa_settings',
            'q_pwa_ios_section',
            ['field' => 'q_pwa_ios_splash_background', 'description' => 'Background color for iOS splash screen']
        );

        add_settings_field(
            'q_pwa_ios_format_detection',
            'Disable iOS Format Detection',
            [self::class, 'render_checkbox_field'],
            'q_pwa_settings',
            'q_pwa_ios_section',
            ['field' => 'q_pwa_ios_format_detection', 'description' => 'Prevent iOS from automatically detecting and formatting phone numbers, emails, etc.']
        );

        add_settings_field(
            'q_pwa_ios_startup_image_enabled',
            'Enable iOS Startup Images',
            [self::class, 'render_checkbox_field'],
            'q_pwa_settings',
            'q_pwa_ios_section',
            ['field' => 'q_pwa_ios_startup_image_enabled', 'description' => 'Generate startup images for different iOS device sizes']
        );

        // iOS Icon Fields
        add_settings_field(
            'q_pwa_ios_icon_57',
            'iOS Icon 57x57',
            [self::class, 'render_media_field'],
            'q_pwa_settings',
            'q_pwa_ios_section',
            ['field' => 'q_pwa_ios_icon_57', 'description' => 'iOS home screen icon for iPhone (57x57 pixels)']
        );

        add_settings_field(
            'q_pwa_ios_icon_72',
            'iOS Icon 72x72',
            [self::class, 'render_media_field'],
            'q_pwa_settings',
            'q_pwa_ios_section',
            ['field' => 'q_pwa_ios_icon_72', 'description' => 'iOS home screen icon for iPad (72x72 pixels)']
        );

        add_settings_field(
            'q_pwa_ios_icon_76',
            'iOS Icon 76x76',
            [self::class, 'render_media_field'],
            'q_pwa_settings',
            'q_pwa_ios_section',
            ['field' => 'q_pwa_ios_icon_76', 'description' => 'iOS home screen icon for iPad (76x76 pixels)']
        );

        add_settings_field(
            'q_pwa_ios_icon_114',
            'iOS Icon 114x114',
            [self::class, 'render_media_field'],
            'q_pwa_settings',
            'q_pwa_ios_section',
            ['field' => 'q_pwa_ios_icon_114', 'description' => 'iOS home screen icon for iPhone Retina (114x114 pixels)']
        );

        add_settings_field(
            'q_pwa_ios_icon_120',
            'iOS Icon 120x120',
            [self::class, 'render_media_field'],
            'q_pwa_settings',
            'q_pwa_ios_section',
            ['field' => 'q_pwa_ios_icon_120', 'description' => 'iOS home screen icon for iPhone Retina (120x120 pixels)']
        );

        add_settings_field(
            'q_pwa_ios_icon_144',
            'iOS Icon 144x144',
            [self::class, 'render_media_field'],
            'q_pwa_settings',
            'q_pwa_ios_section',
            ['field' => 'q_pwa_ios_icon_144', 'description' => 'iOS home screen icon for iPad Retina (144x144 pixels)']
        );

        add_settings_field(
            'q_pwa_ios_icon_152',
            'iOS Icon 152x152',
            [self::class, 'render_media_field'],
            'q_pwa_settings',
            'q_pwa_ios_section',
            ['field' => 'q_pwa_ios_icon_152', 'description' => 'iOS home screen icon for iPad Retina (152x152 pixels)']
        );

        add_settings_field(
            'q_pwa_ios_icon_180',
            'iOS Icon 180x180',
            [self::class, 'render_media_field'],
            'q_pwa_settings',
            'q_pwa_ios_section',
            ['field' => 'q_pwa_ios_icon_180', 'description' => 'iOS home screen icon for iPhone 6 Plus (180x180 pixels)']
        );

        // Android Settings Fields (reusing existing icon and color fields)
        add_settings_field(
            'q_pwa_android_icon_192',
            'Android Icon 192x192',
            [self::class, 'render_media_field'],
            'q_pwa_settings',
            'q_pwa_android_section',
            ['field' => 'q_pwa_icon_192', 'description' => 'App icon for Android devices (192x192 pixels)']
        );

        add_settings_field(
            'q_pwa_android_icon_512',
            'Android Icon 512x512',
            [self::class, 'render_media_field'],
            'q_pwa_settings',
            'q_pwa_android_section',
            ['field' => 'q_pwa_icon_512', 'description' => 'App icon for splash screen (512x512 pixels)']
        );

        add_settings_field(
            'q_pwa_android_theme_color',
            'Android Theme Color',
            [self::class, 'render_color_field'],
            'q_pwa_settings',
            'q_pwa_android_section',
            ['field' => 'q_pwa_theme_color', 'description' => 'Primary theme color for Android status bar and task switcher']
        );

        add_settings_field(
            'q_pwa_android_background_color',
            'Android Background Color',
            [self::class, 'render_color_field'],
            'q_pwa_settings',
            'q_pwa_android_section',
            ['field' => 'q_pwa_background_color', 'description' => 'Background color for Android splash screen']
        );

        // Desktop Settings Fields (reusing existing display and orientation fields)
        add_settings_field(
            'q_pwa_desktop_display_mode',
            'Desktop Display Mode',
            [self::class, 'render_select_field'],
            'q_pwa_settings',
            'q_pwa_desktop_section',
            [
                'field' => 'q_pwa_display_mode',
                'options' => [
                    'standalone' => 'Standalone (Recommended)',
                    'fullscreen' => 'Fullscreen',
                    'minimal-ui' => 'Minimal UI',
                    'browser' => 'Browser'
                ],
                'description' => 'How the app should be displayed when launched on desktop'
            ]
        );

        add_settings_field(
            'q_pwa_desktop_orientation',
            'Desktop Orientation',
            [self::class, 'render_select_field'],
            'q_pwa_settings',
            'q_pwa_desktop_section',
            [
                'field' => 'q_pwa_orientation',
                'options' => [
                    'any' => 'Any',
                    'portrait' => 'Portrait',
                    'landscape' => 'Landscape'
                ],
                'description' => 'Preferred screen orientation for desktop'
            ]
        );

        add_settings_field(
            'q_pwa_desktop_start_url',
            'Desktop Start URL',
            [self::class, 'render_text_field'],
            'q_pwa_settings',
            'q_pwa_desktop_section',
            ['field' => 'q_pwa_start_url', 'description' => 'URL to load when desktop app is launched (relative to site root)']
        );
    }

    public static function render_pwa_settings_page()
    {
        require_once Q_PLUGIN_DIR . 'includes/templates/pwa-settings-page.php';
    }

    // iOS Section Description
    public static function render_ios_section_description()
    {
        echo '<p>Configure iOS-specific Progressive Web App settings for better integration with Apple devices.</p>';
    }

    // Android Section Description
    public static function render_android_section_description()
    {
        echo '<p>Configure Android-specific Progressive Web App settings for optimal integration with Android devices.</p>';
    }

    // Desktop Section Description
    public static function render_desktop_section_description()
    {
        echo '<p>Configure desktop-specific Progressive Web App settings for browsers and operating systems.</p>';
    }

    // iOS Status Bar Style Sanitization
    public static function sanitize_ios_status_bar_style($value)
    {
        $allowed_values = ['default', 'black', 'black-translucent'];
        return in_array($value, $allowed_values) ? $value : 'default';
    }

    // Section descriptions
    public static function render_basic_section_description()
    {
        echo '<p>Configure basic Progressive Web App settings for your site.</p>';
    }

    public static function render_appearance_section_description()
    {
        echo '<p>Customize how your PWA looks and behaves when installed.</p>';
    }

    public static function render_offline_section_description()
    {
        echo '<p>Configure offline functionality and caching strategies.</p>';
    }

    public static function render_icons_section_description()
    {
        echo '<p>Upload icons for your PWA. Icons should be square and in PNG format.</p>';
    }

    // Field renderers
    public static function render_text_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '');
        $description = $args['description'] ?? '';

        echo '<input type="text" name="' . esc_attr($field) . '" value="' . esc_attr($value) . '" class="regular-text" />';
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    public static function render_textarea_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '');
        $description = $args['description'] ?? '';

        echo '<textarea name="' . esc_attr($field) . '" rows="3" cols="50" class="large-text">' . esc_textarea($value) . '</textarea>';
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    public static function render_checkbox_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, false);
        $description = $args['description'] ?? '';

        echo '<input type="checkbox" name="' . esc_attr($field) . '" value="1" ' . checked(1, $value, false) . ' />';
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    public static function render_color_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '#000000');
        $description = $args['description'] ?? '';

        echo '<input type="color" name="' . esc_attr($field) . '" value="' . esc_attr($value) . '" />';
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    public static function render_select_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '');
        $options = $args['options'] ?? [];
        $description = $args['description'] ?? '';

        echo '<select name="' . esc_attr($field) . '">';
        foreach ($options as $option_value => $option_label) {
            echo '<option value="' . esc_attr($option_value) . '" ' . selected($value, $option_value, false) . '>' . esc_html($option_label) . '</option>';
        }
        echo '</select>';
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    public static function render_media_field($args)
    {
        $field = $args['field'];
        $value = get_option($field, '');
        $description = $args['description'] ?? '';

        echo '<input type="url" name="' . esc_attr($field) . '" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<button type="button" class="button q-media-upload" data-field="' . esc_attr($field) . '">Upload Image</button>';
        if ($value) {
            echo '<br><img src="' . esc_url($value) . '" style="max-width: 100px; max-height: 100px; margin-top: 10px;" />';
        }
        if ($description) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
    }

    // Sanitization functions
    public static function sanitize_display_mode($value)
    {
        $allowed = ['standalone', 'fullscreen', 'minimal-ui', 'browser'];
        return in_array($value, $allowed) ? $value : 'standalone';
    }

    public static function sanitize_orientation($value)
    {
        $allowed = ['any', 'portrait', 'landscape'];
        return in_array($value, $allowed) ? $value : 'any';
    }

    public static function sanitize_cache_strategy($value)
    {
        $allowed = ['cache_first', 'network_first', 'stale_while_revalidate'];
        return in_array($value, $allowed) ? $value : 'cache_first';
    }

    // Frontend functionality
    public static function enqueue_pwa_scripts()
    {
        if (!get_option('q_pwa_enabled', false)) {
            return;
        }

        // Enqueue PWA manager script
        wp_enqueue_script(
            'q-pwa-manager',
            Q_PLUGIN_URL . 'includes/js/pwa-manager.js',
            ['jquery'],
            '1.0.0',
            true
        );

        // Enqueue PWA styles
        wp_enqueue_style(
            'q-pwa-styles',
            Q_PLUGIN_URL . 'includes/css/pwa-styles.css',
            [],
            '1.0.0'
        );

        // Pass PWA settings to JavaScript
        wp_localize_script('q-pwa-manager', 'qPWASettings', [
            'enabled' => get_option('q_pwa_enabled', false),
            'offlineEnabled' => get_option('q_pwa_offline_enabled', true),
            'cacheStrategy' => get_option('q_pwa_cache_strategy', 'cache_first'),
            'manifestUrl' => home_url('/manifest.json'),
            'siteUrl' => home_url('/'),
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('q_pwa_nonce'),
            // Offline notification settings
            'offlineNotificationEnabled' => get_option('q_pwa_offline_notification_enabled', true),
            'offlineTitle' => get_option('q_pwa_offline_notification_title', 'You\'re Offline'),
            'offlineMessage' => get_option('q_pwa_offline_notification_message', 'Some features may be limited while offline.'),
            'offlineIcon' => get_option('q_pwa_offline_notification_icon', '📡'),
            'connectionMonitoringEnabled' => get_option('q_pwa_connection_monitoring_enabled', true),
            'slowConnectionNotification' => get_option('q_pwa_slow_connection_notification', true)
        ]);
    }

    public static function add_pwa_meta_tags()
    {
        if (!get_option('q_pwa_enabled', false)) {
            return;
        }

        $theme_color = get_option('q_pwa_theme_color', '#000000');
        $app_name = get_option('q_pwa_app_name', get_bloginfo('name'));
        $ios_status_bar_style = get_option('q_pwa_ios_status_bar_style', 'default');
        $ios_splash_background = get_option('q_pwa_ios_splash_background', '#ffffff');
        $ios_format_detection = get_option('q_pwa_ios_format_detection', false);

        // Basic PWA meta tags
        echo '<meta name="theme-color" content="' . esc_attr($theme_color) . '">' . "\n";
        echo '<meta name="mobile-web-app-capable" content="yes">' . "\n";
        echo '<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">' . "\n";

        // iOS-specific meta tags
        echo '<meta name="apple-mobile-web-app-capable" content="yes">' . "\n";
        echo '<meta name="apple-mobile-web-app-status-bar-style" content="' . esc_attr($ios_status_bar_style) . '">' . "\n";
        echo '<meta name="apple-mobile-web-app-title" content="' . esc_attr($app_name) . '">' . "\n";

        // iOS format detection
        if ($ios_format_detection) {
            echo '<meta name="format-detection" content="telephone=no, email=no, address=no">' . "\n";
        }

        // iOS touch icons
        self::add_ios_touch_icons();

        // iOS startup images
        if (get_option('q_pwa_ios_startup_image_enabled', false)) {
            self::add_ios_startup_images($ios_splash_background);
        }
    }

    // Add iOS touch icons
    public static function add_ios_touch_icons()
    {
        $ios_icons = [
            '57' => get_option('q_pwa_ios_icon_57', ''),
            '72' => get_option('q_pwa_ios_icon_72', ''),
            '76' => get_option('q_pwa_ios_icon_76', ''),
            '114' => get_option('q_pwa_ios_icon_114', ''),
            '120' => get_option('q_pwa_ios_icon_120', ''),
            '144' => get_option('q_pwa_ios_icon_144', ''),
            '152' => get_option('q_pwa_ios_icon_152', ''),
            '180' => get_option('q_pwa_ios_icon_180', '')
        ];

        // Add touch icons for each size
        foreach ($ios_icons as $size => $icon_url) {
            if (!empty($icon_url)) {
                echo '<link rel="apple-touch-icon" sizes="' . esc_attr($size . 'x' . $size) . '" href="' . esc_url($icon_url) . '">' . "\n";
            }
        }

        // Fallback to main PWA icons if no iOS-specific icons are set
        if (empty(array_filter($ios_icons))) {
            $icon_192 = get_option('q_pwa_icon_192', '');
            $icon_512 = get_option('q_pwa_icon_512', '');

            if (!empty($icon_192)) {
                echo '<link rel="apple-touch-icon" sizes="192x192" href="' . esc_url($icon_192) . '">' . "\n";
            }
            if (!empty($icon_512)) {
                echo '<link rel="apple-touch-icon" sizes="512x512" href="' . esc_url($icon_512) . '">' . "\n";
            }
        }
    }

    // Add iOS startup images (splash screens)
    public static function add_ios_startup_images($background_color = '#ffffff')
    {
        // iOS startup images for different device sizes
        $startup_images = [
            // iPhone
            ['width' => 320, 'height' => 568, 'ratio' => 2, 'orientation' => 'portrait'],
            ['width' => 375, 'height' => 667, 'ratio' => 2, 'orientation' => 'portrait'],
            ['width' => 414, 'height' => 736, 'ratio' => 3, 'orientation' => 'portrait'],
            ['width' => 375, 'height' => 812, 'ratio' => 3, 'orientation' => 'portrait'],
            ['width' => 414, 'height' => 896, 'ratio' => 2, 'orientation' => 'portrait'],
            ['width' => 414, 'height' => 896, 'ratio' => 3, 'orientation' => 'portrait'],
            // iPad
            ['width' => 768, 'height' => 1024, 'ratio' => 1, 'orientation' => 'portrait'],
            ['width' => 768, 'height' => 1024, 'ratio' => 2, 'orientation' => 'portrait'],
            ['width' => 834, 'height' => 1112, 'ratio' => 2, 'orientation' => 'portrait'],
            ['width' => 834, 'height' => 1194, 'ratio' => 2, 'orientation' => 'portrait'],
            ['width' => 1024, 'height' => 1366, 'ratio' => 2, 'orientation' => 'portrait'],
        ];

        foreach ($startup_images as $image) {
            $media_query = "(device-width: {$image['width']}px) and (device-height: {$image['height']}px) and (-webkit-device-pixel-ratio: {$image['ratio']}) and (orientation: {$image['orientation']})";
            $startup_url = self::generate_startup_image_url($image['width'], $image['height'], $image['ratio'], $background_color);

            echo '<link rel="apple-touch-startup-image" media="' . esc_attr($media_query) . '" href="' . esc_url($startup_url) . '">' . "\n";
        }
    }

    // Generate startup image URL (placeholder implementation)
    public static function generate_startup_image_url($width, $height, $ratio, $background_color)
    {
        // For now, return a data URL with solid color
        // In a full implementation, you might generate actual images
        $actual_width = $width * $ratio;
        $actual_height = $height * $ratio;

        return "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='{$actual_width}' height='{$actual_height}' viewBox='0 0 {$actual_width} {$actual_height}'%3E%3Crect width='100%25' height='100%25' fill='{$background_color}'/%3E%3C/svg%3E";
    }

    public static function add_manifest_link()
    {
        if (!get_option('q_pwa_enabled', false)) {
            return;
        }

        echo '<link rel="manifest" href="' . esc_url(home_url('/manifest.json')) . '">' . "\n";
    }

    // Check PWA requirements
    public static function check_pwa_requirements()
    {
        $requirements = [
            'https' => is_ssl(),
            'manifest' => get_option('q_pwa_enabled', false),
            'service_worker' => file_exists(ABSPATH . 'firebase-messaging-sw.js'),
            'icons' => !empty(get_option('q_pwa_icon_192', '')) && !empty(get_option('q_pwa_icon_512', ''))
        ];

        return $requirements;
    }

    // Get PWA status
    public static function get_pwa_status()
    {
        $requirements = self::check_pwa_requirements();
        $total = count($requirements);
        $met = count(array_filter($requirements));

        return [
            'requirements' => $requirements,
            'percentage' => round(($met / $total) * 100),
            'ready' => $met === $total
        ];
    }

    // Enqueue admin scripts and styles
    public static function enqueue_admin_scripts($hook)
    {
        // Debug: Log the hook name
        error_log('PWA Admin: Hook name: ' . $hook);

        // Only load on PWA settings page
        if ($hook !== 'settings_page_q-pwa-settings') {
            error_log('PWA Admin: Not on PWA settings page, skipping enqueue');
            return;
        }

        error_log('PWA Admin: Enqueuing admin scripts and styles');

        // Enqueue admin CSS
        wp_enqueue_style(
            'q-pwa-admin-styles',
            Q_PLUGIN_URL . 'includes/css/pwa-admin.css',
            [],
            '1.0.1'
        );

        // Enqueue admin JavaScript
        wp_enqueue_script(
            'q-pwa-admin-script',
            Q_PLUGIN_URL . 'includes/js/pwa-admin.js',
            ['jquery', 'wp-media'],
            '1.0.1',
            true
        );

        // Pass data to JavaScript
        wp_localize_script('q-pwa-admin-script', 'qPWAAdmin', [
            'manifestUrl' => Q_PWA_Manifest::get_manifest_url(),
            'siteUrl' => home_url('/'),
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('q_pwa_admin_nonce'),
            'isFirstTime' => !get_option('q_pwa_enabled', false)
        ]);

        error_log('PWA Admin: Scripts and styles enqueued successfully');
    }

    /**
     * Regenerate the offline page when PWA settings are updated
     */
    public static function regenerate_offline_page()
    {
        if (function_exists('q_generate_offline_page')) {
            q_generate_offline_page();
        }
    }
}
