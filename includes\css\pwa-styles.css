/**
 * PWA Styles - Progressive Web App specific styles
 */

/* PWA Install Prompt */
.q-pwa-install-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.q-pwa-install-container.hidden {
  transform: translateY(100%);
  opacity: 0;
  pointer-events: none;
}

.q-pwa-install-prompt {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.q-pwa-install-content {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.q-pwa-install-icon {
  font-size: 48px;
  flex-shrink: 0;
}

.q-pwa-install-text {
  flex: 1;
}

.q-pwa-install-text h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.q-pwa-install-text p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.q-pwa-install-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-shrink: 0;
}

.q-pwa-btn-primary {
  background: #007cba;
  color: #fff;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s ease;
}

.q-pwa-btn-primary:hover {
  background: #005a87;
}

.q-pwa-btn-secondary {
  background: transparent;
  color: #666;
  border: 1px solid #ddd;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.q-pwa-btn-secondary:hover {
  background: #f5f5f5;
  color: #333;
}

/* PWA Notifications */
.q-pwa-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  max-width: 380px;
  min-width: 300px;
  animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.q-pwa-notification.hiding {
  animation: slideOutRight 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.q-pwa-notification.offline {
  background: #ff9800;
  color: #fff;
  border-color: #f57c00;
}

.q-pwa-notification.online {
  background: #4caf50;
  color: #fff;
  border-color: #388e3c;
}

.q-pwa-notification.slow-connection {
  background: #ff5722;
  color: #fff;
  border-color: #d84315;
}

.q-pwa-notification-content {
  padding: 16px;
}

.q-pwa-notification-main {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
}

.q-pwa-notification-icon {
  font-size: 24px;
  flex-shrink: 0;
  line-height: 1;
}

.q-pwa-notification-text {
  flex: 1;
  min-width: 0;
}

.q-pwa-notification-title {
  font-weight: 600;
  font-size: 16px;
  line-height: 1.3;
  margin-bottom: 4px;
}

.q-pwa-notification-message {
  font-size: 14px;
  line-height: 1.4;
  opacity: 0.9;
}

.q-pwa-notification-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 12px;
}

.q-pwa-notification-btn {
  background: rgba(255, 255, 255, 0.2);
  color: inherit;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.q-pwa-notification-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.q-pwa-notification-btn:active {
  transform: translateY(0);
}

.q-pwa-notification-btn.primary {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  font-weight: 600;
}

.q-pwa-notification-btn.primary:hover {
  background: #fff;
}

.q-pwa-notification-btn.close {
  background: transparent;
  border: none;
  padding: 4px 8px;
  font-size: 18px;
  font-weight: bold;
  opacity: 0.7;
  min-width: auto;
}

.q-pwa-notification-btn.close:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.1);
}

.q-pwa-notification-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.q-pwa-notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.3);
  width: 100%;
  animation: progressBar linear forwards;
}

/* White notification variant */
.q-pwa-notification:not(.offline):not(.online):not(.slow-connection) {
  background: #fff;
  color: #333;
  border-color: #e0e0e0;
}

.q-pwa-notification:not(.offline):not(.online):not(.slow-connection)
  .q-pwa-notification-btn {
  background: #f5f5f5;
  color: #333;
  border-color: #ddd;
}

.q-pwa-notification:not(.offline):not(.online):not(.slow-connection)
  .q-pwa-notification-btn:hover {
  background: #e0e0e0;
}

.q-pwa-notification:not(.offline):not(.online):not(.slow-connection)
  .q-pwa-notification-btn.primary {
  background: #007cba;
  color: #fff;
  border-color: #007cba;
}

.q-pwa-notification:not(.offline):not(.online):not(.slow-connection)
  .q-pwa-notification-btn.primary:hover {
  background: #005a87;
}

/* PWA App Shell */
.pwa-enabled {
  /* Base styles for PWA-enabled sites */
}

.pwa-installed {
  /* Styles when app is installed */
}

.pwa-installed .site-header {
  /* Adjust header for standalone mode */
  padding-top: env(safe-area-inset-top);
}

.pwa-installed .site-footer {
  /* Adjust footer for standalone mode */
  padding-bottom: env(safe-area-inset-bottom);
}

/* iOS Standalone Mode */
.ios-standalone {
  /* iOS-specific standalone styles */
}

.ios-standalone .site-header {
  padding-top: 44px; /* Status bar height */
}

/* iOS Device Enhancements */
.ios-device {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.ios-pwa-mode {
  /* iOS standalone mode styles */
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

/* iOS status bar styles */
.ios-pwa-mode .header,
.ios-pwa-mode .navbar {
  padding-top: calc(env(safe-area-inset-top) + 10px);
}

.ios-pwa-mode .footer {
  padding-bottom: calc(env(safe-area-inset-bottom) + 10px);
}

/* iOS touch icon preview styles */
.q-ios-icon-preview {
  display: inline-block;
  width: 60px;
  height: 60px;
  border-radius: 12px;
  overflow: hidden;
  margin-top: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.q-ios-icon-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* iOS installation prompt styles */
#q-ios-install-prompt {
  animation: slideInUp 0.3s ease-out;
}

/* iOS PWA info section styles */
.q-pwa-ios-info {
  margin-top: 30px;
}

.q-ios-installation-guide {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-top: 15px;
}

.q-ios-installation-guide h4 {
  margin-top: 0;
  color: #1d2327;
}

.q-ios-installation-guide ol {
  margin: 10px 0 0 20px;
}

.q-ios-installation-guide li {
  margin-bottom: 8px;
  line-height: 1.5;
}

/* Platform-specific installation guides */
.q-android-installation-guide,
.q-desktop-installation-guide {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-top: 15px;
}

.q-android-installation-guide h4,
.q-desktop-installation-guide h4 {
  margin-top: 0;
  color: #1d2327;
}

.q-android-installation-guide ol,
.q-desktop-installation-guide ol {
  margin: 10px 0 0 20px;
}

.q-android-installation-guide li,
.q-desktop-installation-guide li {
  margin-bottom: 8px;
  line-height: 1.5;
}

/* Platform info sections */
.q-pwa-android-info,
.q-pwa-desktop-info {
  margin-top: 30px;
}

/* Quick setup guide styles */
.q-pwa-quick-setup {
  margin-top: 30px;
}

.q-setup-steps {
  display: grid;
  gap: 20px;
  margin-top: 20px;
}

.setup-step {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007cba;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #007cba;
  color: white;
  border-radius: 50%;
  font-weight: bold;
  font-size: 14px;
  flex-shrink: 0;
}

.step-content h4 {
  margin: 0 0 8px 0;
  color: #1d2327;
  font-size: 16px;
}

.step-content p {
  margin: 0;
  color: #50575e;
  line-height: 1.5;
}

/* Responsive Design for PWA */
@media (display-mode: standalone) {
  /* Styles when running as installed PWA */
  body {
    user-select: none; /* Prevent text selection in app mode */
  }

  /* Hide browser-specific elements */
  .browser-only {
    display: none !important;
  }

  /* Show PWA-specific elements */
  .pwa-only {
    display: block !important;
  }
}

@media (display-mode: fullscreen) {
  /* Styles for fullscreen mode */
  .site-header,
  .site-footer {
    display: none;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .q-pwa-install-content {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .q-pwa-install-actions {
    flex-direction: row;
    justify-content: center;
    width: 100%;
  }

  .q-pwa-notification {
    right: 10px;
    left: 10px;
    max-width: none;
    min-width: auto;
    top: 10px;
  }

  .q-pwa-notification-main {
    margin-bottom: 8px;
  }

  .q-pwa-notification-actions {
    flex-direction: column;
    gap: 6px;
  }

  .q-pwa-notification-btn {
    width: 100%;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .q-pwa-install-prompt {
    padding: 15px;
  }

  .q-pwa-install-content {
    padding: 15px;
  }

  .q-pwa-install-text h3 {
    font-size: 16px;
  }

  .q-pwa-install-text p {
    font-size: 13px;
  }

  .q-pwa-btn-primary {
    padding: 10px 20px;
    font-size: 14px;
  }
}

/* Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes progressBar {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* Loading States */
.pwa-loading {
  position: relative;
  overflow: hidden;
}

.pwa-loading::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #007cba, transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Offline Indicators */
.offline-indicator {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #ff9800;
  color: #fff;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  z-index: 9998;
  animation: slideInUp 0.3s ease;
}

/* PWA Splash Screen Styles */
.pwa-splash {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--pwa-background-color, #ffffff);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 99999;
  transition: opacity 0.5s ease;
}

.pwa-splash.hidden {
  opacity: 0;
  pointer-events: none;
}

.pwa-splash-logo {
  width: 120px;
  height: 120px;
  margin-bottom: 20px;
  animation: pulse 2s infinite;
}

.pwa-splash-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--pwa-theme-color, #000000);
  margin-bottom: 10px;
}

.pwa-splash-subtitle {
  font-size: 14px;
  color: #666;
  text-align: center;
  max-width: 300px;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .q-pwa-install-icon {
    /* High-resolution icon styles */
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .q-pwa-install-content {
    background: #2c2c2c;
    color: #fff;
  }

  .q-pwa-install-text h3 {
    color: #fff;
  }

  .q-pwa-install-text p {
    color: #ccc;
  }

  .q-pwa-notification {
    background: #2c2c2c;
    border-color: #444;
    color: #fff;
  }
}

/* Print Styles */
@media print {
  .q-pwa-install-container,
  .q-pwa-notification,
  .offline-indicator {
    display: none !important;
  }
}
